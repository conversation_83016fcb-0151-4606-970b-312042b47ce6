/**
 * Feature extraction for the ML-based activity prioritization system
 * This module extracts relevant features from activities for classification.
 */
import { differenceInDays, parseISO, isAfter, isBefore, startOfTomorrow, endOfToday, isSameDay } from 'date-fns';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Activity interface matching our Supabase schema
export interface Activity {
  id: string;
  tenant_id: string;
  user_id: string | null;
  type: string;
  target_id: string | null;
  target_type: string | null;
  metadata: Record<string, unknown>; // JSONB in Supabase
  created_at: string;
}

// Target metadata interfaces
interface CaseMetadata {
  metadata?: Record<string, unknown>;
}

interface DocumentMetadata {
  metadata?: Record<string, unknown>;
}

interface TaskMetadata {
  deadline?: string;
  priority?: string;
  metadata?: Record<string, unknown>;
}

// Define the features we'll extract for classification
export interface ActivityFeatures {
  daysSinceCreation: number;
  daysSinceLastUpdate: number;
  daysToDeadline: number;
  hasDeadline: boolean;
  hasStatutoryLimit: boolean;
  isUrgent: boolean;
  viewCount: number;
  interactionCount: number;
  completionPercentage: number;
  isDueToday: boolean;
  isDueTomorrow: boolean;
  isOverdue: boolean;
}

/**
 * Extract deadline-related information from activity and target metadata
 */
function extractDeadlineInfo(
  activity: Activity,
  targetMetadata: Record<string, unknown> | null
): {
  deadline: Date | null;
  hasStatutoryLimit: boolean;
  isUrgent: boolean;
} {
  const now = new Date();
  let deadline: Date | null = null;
  let hasStatutoryLimit = false;
  let isUrgent = false;

  // Check activity metadata first
  if (activity.metadata) {
    // Direct deadline in activity
    if ((activity.metadata as any).deadline) {
      deadline = parseISO((activity.metadata as any).deadline);
    }

    // Urgency flag
    if ((activity.metadata as any).urgent === true) {
      isUrgent = true;
    }

    // Statute of limitations
    if ((activity.metadata as any).statute_of_limitations) {
      hasStatutoryLimit = true;
      // Use statute date as deadline if earlier than current deadline
      const statuteDate = parseISO((activity.metadata as any).statute_of_limitations);
      if (!deadline || isBefore(statuteDate, deadline)) {
        deadline = statuteDate;
      }
    }
  }

  // Then check target metadata (if available)
  if (targetMetadata) {
    // Target deadline
    if (targetMetadata.deadline && (!deadline || isBefore(parseISO(String(targetMetadata.deadline)), deadline))) {
      deadline = parseISO(String(targetMetadata.deadline));
    }

    // Target urgency flag
    if (targetMetadata.urgent === true) {
      isUrgent = true;
    }

    // Target statute of limitations
    if (targetMetadata.statute_of_limitations) {
      hasStatutoryLimit = true;
      const statuteDate = parseISO(String(targetMetadata.statute_of_limitations));
      if (!deadline || isBefore(statuteDate, deadline)) {
        deadline = statuteDate;
      }
    }

    // Legal filing deadlines are often critical
    if (targetMetadata.filing_deadline && (!deadline || isBefore(parseISO(targetMetadata.filing_deadline), deadline))) {
      deadline = parseISO(targetMetadata.filing_deadline);
    }
  }

  return { deadline, hasStatutoryLimit, isUrgent };
}

/**
 * Get target entity metadata (case, document, etc.)
 */
async function getTargetMetadata(activity: Activity): Promise<Record<string, unknown> | null> {
  if (!activity.target_id || !activity.target_type) {
    return null;
  }

  try {
    let metadata: Record<string, unknown> | null = null;

    // Query appropriate table based on target_type
    switch (activity.target_type.toLowerCase()) {
      case 'case':
        const { data: caseData } = await supabase
          .from('cases')
          .select('metadata')
          .eq('id', activity.target_id)
          .eq('tenant_id', activity.tenant_id)
          .single();
        metadata = (caseData as CaseMetadata | null)?.metadata || null;
        break;

      case 'document':
        const { data: docData } = await supabase
          .from('documents')
          .select('metadata')
          .eq('id', activity.target_id)
          .eq('tenant_id', activity.tenant_id)
          .single();
        metadata = (docData as DocumentMetadata | null)?.metadata || null;
        break;

      case 'task':
        const { data: taskData } = await supabase
          .from('tasks')
          .select('deadline, priority, metadata')
          .eq('id', activity.target_id)
          .eq('tenant_id', activity.tenant_id)
          .single();

        if (taskData as TaskMetadata | null) {
          const task = taskData as TaskMetadata;
          metadata = {
            ...(task.metadata || {}),
            deadline: task.deadline,
            priority: task.priority
          };
        }
        break;

      // Add other entity types as needed
    }

    return metadata;
  } catch (error) {
    console.error('Error fetching target metadata:', error);
    return null;
  }
}

/**
 * Get activity interaction metrics
 */
async function getActivityMetrics(activity: Activity): Promise<{
  viewCount: number;
  interactionCount: number;
  lastUpdateTimestamp: string | null;
  completionPercentage: number;
}> {
  // Default values
  const result: {
    viewCount: number;
    interactionCount: number;
    lastUpdateTimestamp: string | null;
    completionPercentage: number;
  } = {
    viewCount: 0,
    interactionCount: 0,
    lastUpdateTimestamp: null,
    completionPercentage: 0
  };

  try {
    // Count views (activities of type 'view' related to this target)
    if (activity.target_id && activity.target_type) {
      const { count: viewCount, error: viewError } = await supabase
        .from('activities')
        .select('id', { count: 'exact', head: true })
        .eq('target_id', activity.target_id)
        .eq('target_type', activity.target_type)
        .eq('tenant_id', activity.tenant_id)
        .eq('type', 'view');

      if (!viewError && viewCount !== null) {
        result.viewCount = viewCount;
      }

      // Count other interactions
      const { count: interactionCount, error: intError } = await supabase
        .from('activities')
        .select('id', { count: 'exact', head: true })
        .eq('target_id', activity.target_id)
        .eq('target_type', activity.target_type)
        .eq('tenant_id', activity.tenant_id)
        .neq('type', 'view'); // All non-view activities

      if (!intError && interactionCount !== null) {
        result.interactionCount = interactionCount;
      }

      // Get most recent update
      const { data: latestActivity, error: latestError } = await supabase
        .from('activities')
        .select('created_at')
        .eq('target_id', activity.target_id)
        .eq('target_type', activity.target_type)
        .eq('tenant_id', activity.tenant_id)
        .eq('type', 'update')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (!latestError && latestActivity) {
        result.lastUpdateTimestamp = (latestActivity as { created_at: string }).created_at || null;
      }

      // Get completion percentage from target metadata if available
      const targetMeta = await getTargetMetadata(activity);
      if (targetMeta && typeof targetMeta.completion_percentage === 'number') {
        result.completionPercentage = targetMeta.completion_percentage;
      } else if (targetMeta && typeof targetMeta.progress === 'number') {
        result.completionPercentage = targetMeta.progress;
      } else if (activity.metadata && typeof (activity.metadata as any).completion_percentage === 'number') {
        result.completionPercentage = (activity.metadata as any).completion_percentage;
      }
    }

    return result;
  } catch (error) {
    console.error('Error getting activity metrics:', error);
    return result;
  }
}

/**
 * Extract all features from an activity for ML classification
 */
export async function extractActivityFeatures(activity: Activity): Promise<ActivityFeatures> {
  const now = new Date();
  const createdAt = parseISO(activity.created_at);

  // Get target entity metadata
  const targetMetadata = await getTargetMetadata(activity);

  // Extract deadline information
  const { deadline, hasStatutoryLimit, isUrgent: initialUrgency } = extractDeadlineInfo(activity, targetMetadata);
  let isUrgent = initialUrgency;

  // Get interaction metrics
  const { viewCount, interactionCount, lastUpdateTimestamp, completionPercentage } =
    await getActivityMetrics(activity);

  // Calculate temporal features
  const daysSinceCreation = differenceInDays(now, createdAt);

  let daysSinceLastUpdate = daysSinceCreation;
  if (lastUpdateTimestamp) {
    daysSinceLastUpdate = differenceInDays(now, parseISO(lastUpdateTimestamp));
  }

  // Deadline-related features
  let daysToDeadline = 999; // Large default value when no deadline
  let isDueToday = false;
  let isDueTomorrow = false;
  let isOverdue = false;

  if (deadline) {
    daysToDeadline = differenceInDays(deadline, now);
    isDueToday = isSameDay(deadline, now);
    isDueTomorrow = isSameDay(deadline, startOfTomorrow());
    isOverdue = isBefore(deadline, now);
  }

  // Special case: LLM-detected urgency in text content
  if ((activity.metadata as any)?.llm_analysis?.urgency_detected === true) {
    isUrgent = true;
  }

  // Statute of limitations urgent if within 60 days
  if (hasStatutoryLimit && deadline && daysToDeadline <= 60) {
    isUrgent = true;
  }

  return {
    daysSinceCreation,
    daysSinceLastUpdate,
    daysToDeadline,
    hasDeadline: !!deadline,
    hasStatutoryLimit,
    isUrgent,
    viewCount,
    interactionCount,
    completionPercentage,
    isDueToday,
    isDueTomorrow,
    isOverdue
  };
}

/**
 * Analyze activity text using LLM for urgency detection
 * Note: This would be implemented separately and integrated with OpenAI or another provider
 */
export async function detectUrgencyWithLLM(activity: Activity): Promise<boolean> {
  // This would be a placeholder for actual LLM integration
  // Based on the memory, this system already has OpenAI integration

  // For now, return false as a placeholder
  // In a real implementation, this would send the activity description/content
  // to an LLM to analyze for urgency indicators
  return false;
}
